==========================
Django 1.5.1 release notes
==========================

*March 28, 2013*

This is Django 1.5.1, a bugfix release for Django 1.5. It's completely backwards
compatible with Django 1.5, but includes a handful of fixes.

The biggest fix is for a memory leak introduced in Django 1.5. Under certain
circumstances, repeated iteration over querysets could leak memory - sometimes
quite a bit of it. If you'd like more information, the details are in
:ticket:`our ticket tracker <19895>` (and in :bpo:`a related issue <17468>` in
Python itself).

If you've noticed memory problems under Django 1.5, upgrading to 1.5.1 should
fix those issues.

Django 1.5.1 also includes a couple smaller fixes:

* Module-level warnings emitted during tests are no longer silently hidden
  (:ticket:`18985`).
* Prevented filtering on password hashes in the user admin (:ticket:`20078`).
