==========================
Django 1.4.2 release notes
==========================

*October 17, 2012*

This is the second security release in the Django 1.4 series.

Host header poisoning
=====================

Some parts of Django -- independent of end-user-written applications -- make
use of full URLs, including domain name, which are generated from the HTTP Host
header. Some attacks against this are beyond Django's ability to control, and
require the web server to be properly configured; Django's documentation has
for some time contained notes advising users on such configuration.

Django's own built-in parsing of the Host header is, however, still vulnerable,
as was reported to us recently. The Host header parsing in Django 1.3.3 and
Django 1.4.1 -- specifically, ``django.http.HttpRequest.get_host()`` -- was
incorrectly handling username/password information in the header. Thus, for
example, the following Host header would be accepted by Django when running on
``validsite.com``:

.. code-block:: text

    Host: validsite.com:<EMAIL>

Using this, an attacker can cause parts of Django -- particularly the
password-reset mechanism -- to generate and display arbitrary URLs to users.

To remedy this, the parsing in ``HttpRequest.get_host()`` is being modified;
Host headers which contain potentially dangerous content (such as
username/password pairs) now raise the exception
:exc:`django.core.exceptions.SuspiciousOperation`.

Details of this issue were initially posted online as a `security advisory`_.

.. _security advisory: https://www.djangoproject.com/weblog/2012/oct/17/security/

Backwards incompatible changes
==============================

* The newly introduced :class:`~django.db.models.GenericIPAddressField`
  constructor arguments have been adapted to match those of all other model
  fields. The first two keyword arguments are now verbose_name and name.

Other bugfixes and changes
==========================

* Subclass HTMLParser only for appropriate Python versions (#18239).
* Added batch_size argument to qs.bulk_create() (#17788).
* Fixed a small regression in the admin filters where wrongly formatted dates passed as url parameters caused an unhandled ValidationError (#18530).
* Fixed an endless loop bug when accessing permissions in templates (#18979)
* Fixed some Python 2.5 compatibility issues
* Fixed an issue with quoted filenames in Content-Disposition header (#19006)
* Made the context option in ``trans`` and ``blocktrans`` tags accept literals wrapped in single quotes (#18881).
* Numerous documentation improvements and fixes.
