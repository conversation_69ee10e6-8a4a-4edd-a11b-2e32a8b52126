==========================
Django 1.9.1 release notes
==========================

*January 2, 2016*

Django 1.9.1 fixes several bugs in 1.9.

Bugfixes
========

* Fixed ``BaseCache.get_or_set()`` with the ``DummyCache`` backend
  (:ticket:`25840`).

* Fixed a regression in ``FormMixin`` causing forms to be validated twice
  (:ticket:`25548`, :ticket:`26018`).

* Fixed a system check crash with nested ``ArrayField``\s (:ticket:`25867`).

* Fixed a state bug when migrating a ``SeparateDatabaseAndState`` operation
  backwards (:ticket:`25896`).

* Fixed a regression in ``CommonMiddleware`` causing ``If-None-Match`` checks
  to always return HTTP 200 (:ticket:`25900`).

* Fixed missing ``varchar/text_pattern_ops`` index on ``Char<PERSON>ield`` and
  ``TextField`` respectively when using ``AlterField`` on PostgreSQL
  (:ticket:`25412`).

* Fixed admin's delete confirmation page's summary counts of related objects
  (:ticket:`25883`).

* Added ``from __future__ import unicode_literals`` to the default ``apps.py``
  created by ``startapp`` on Python 2 (:ticket:`25909`). Add this line to your
  own ``apps.py`` files created using Django 1.9 if you want your migrations
  to work on both Python 2 and Python 3.

* Prevented ``QuerySet.delete()`` from crashing on MySQL when querying across
  relations (:ticket:`25882`).

* Fixed evaluation of zero-length slices of ``QuerySet.values()``
  (:ticket:`25894`).

* Fixed a state bug when using an ``AlterModelManagers`` operation
  (:ticket:`25852`).

* Fixed ``TypedChoiceField`` change detection with nullable fields
  (:ticket:`25942`).

* Fixed incorrect timezone warnings in custom admin templates that don't have
  a ``data-admin-utc-offset`` attribute in the ``body`` tag.
  (:ticket:`25845`).

* Fixed a regression which prevented using a language not in Django's default
  language list (:setting:`LANGUAGES`) (:ticket:`25915`).

* Avoided hiding some exceptions, like an invalid ``INSTALLED_APPS`` setting,
  behind ``AppRegistryNotReady`` when starting ``runserver`` (:ticket:`25510`).
  This regression appeared in 1.8.5 as a side effect of fixing :ticket:`24704`
  and by mistake the fix wasn't applied to the ``stable/1.9.x`` branch.

* Fixed ``migrate --fake-initial`` detection of many-to-many tables
  (:ticket:`25922`).

* Restored the functionality of the admin's ``list_editable`` add and change
  buttons (:ticket:`25903`).

* Fixed ``isnull`` query lookup for ``ForeignObject``
  (:ticket:`25972`).

* Fixed a regression in the admin which ignored line breaks in read-only fields
  instead of converting them to ``<br>`` (:ticket:`25465`).

* Fixed incorrect object reference in
  ``SingleObjectMixin.get_context_object_name()`` (:ticket:`26006`).

* Made ``loaddata`` skip disabling and enabling database constraints when it
  doesn't load any fixtures (:ticket:`23372`).

* Restored ``contrib.auth`` hashers compatibility with ``py-bcrypt``
  (:ticket:`26016`).

* Fixed a crash in ``QuerySet.values()/values_list()`` after an ``annotate()``
  and ``order_by()`` when ``values()/values_list()`` includes a field not in
  the ``order_by()`` (:ticket:`25316`).
