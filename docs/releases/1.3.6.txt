==========================
Django 1.3.6 release notes
==========================

*February 19, 2013*

Django 1.3.6 fixes four security issues present in previous Django releases in
the 1.3 series.

This is the sixth bugfix/security release in the Django 1.3 series.


Host header poisoning
=====================

Some parts of Django -- independent of end-user-written applications -- make
use of full URLs, including domain name, which are generated from the HTTP Host
header. Django's documentation has for some time contained notes advising users
on how to configure web servers to ensure that only valid Host headers can reach
the Django application. However, it has been reported to us that even with the
recommended web server configurations there are still techniques available for
tricking many common web servers into supplying the application with an
incorrect and possibly malicious Host header.

For this reason, Django 1.3.6 adds a new setting, ``ALLOWED_HOSTS``, which
should contain an explicit list of valid host/domain names for this site. A
request with a Host header not matching an entry in this list will raise
``SuspiciousOperation`` if ``request.get_host()`` is called. For full details
see the documentation for the :setting:`ALLOWED_HOSTS` setting.

The default value for this setting in Django 1.3.6 is ``['*']`` (matching any
host), for backwards-compatibility, but we strongly encourage all sites to set
a more restrictive value.

This host validation is disabled when ``DEBUG`` is ``True`` or when running tests.


XML deserialization
===================

The XML parser in the Python standard library is vulnerable to a number of
attacks via external entities and entity expansion. Django uses this parser for
deserializing XML-formatted database fixtures. The fixture deserializer is not
intended for use with untrusted data, but in order to err on the side of safety
in Django 1.3.6 the XML deserializer refuses to parse an XML document with a
DTD (DOCTYPE definition), which closes off these attack avenues.

These issues in the Python standard library are CVE-2013-1664 and
CVE-2013-1665. More information available `from the Python security team`_.

Django's XML serializer does not create documents with a DTD, so this should
not cause any issues with the typical round-trip from ``dumpdata`` to
``loaddata``, but if you feed your own XML documents to the ``loaddata``
management command, you will need to ensure they do not contain a DTD.

.. _from the Python security team: https://blog.python.org/2013/02/announcing-defusedxml-fixes-for-xml.html


Formset memory exhaustion
=========================

Previous versions of Django did not validate or limit the form-count data
provided by the client in a formset's management form, making it possible to
exhaust a server's available memory by forcing it to create very large numbers
of forms.

In Django 1.3.6, all formsets have a strictly-enforced maximum number of forms
(1000 by default, though it can be set higher via the ``max_num`` formset
factory argument).


Admin history view information leakage
======================================

In previous versions of Django, an admin user without change permission on a
model could still view the Unicode representation of instances via their admin
history log. Django 1.3.6 now limits the admin history log view for an object
to users with change permission for that model.
