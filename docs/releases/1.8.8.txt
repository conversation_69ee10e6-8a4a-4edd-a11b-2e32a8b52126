==========================
Django 1.8.8 release notes
==========================

*January 2, 2016*

Django 1.8.8 fixes several bugs in 1.8.7.

Python 3.2 users, please be advised that we've decided to drop support for
Python 3.2 in Django 1.8.x at the end of 2016. We won't break things
intentionally after that, but we won't test subsequent releases against Python
3.2 either. Upstream support for Python 3.2 ends February 2016 so we don't find
much value in providing security updates for a version of Python that could be
insecure. To read more about the decision and to let us know if this will be
problematic for you, please read the `django-developers thread
<https://groups.google.com/g/django-developers/c/eMu5UQpUdWs/discussion>`_.

Bugfixes
========

* Fixed incorrect ``unique_together`` field name generation by ``inspectdb``
  (:ticket:`25274`).

* Corrected ``__len`` query lookup on ``ArrayField`` for empty arrays
  (:ticket:`25772`).

* Restored the ability to use custom formats from ``formats.py`` with
  ``django.utils.formats.get_format()`` and the ``date`` template filter
  (:ticket:`25812`).

* Fixed a state bug when migrating a ``SeparateDatabaseAndState`` operation
  backwards (:ticket:`25896`).

* Fixed missing ``varchar/text_pattern_ops`` index on ``CharField`` and
  ``TextField`` respectively when using ``AlterField`` on PostgreSQL
  (:ticket:`25412`).

* Fixed a state bug when using an ``AlterModelManagers`` operation
  (:ticket:`25852`).

* Fixed a regression which prevented using a language not in Django's default
  language list (:setting:`LANGUAGES`) (:ticket:`25915`).

* ``django.views.decorators.cache.never_cache()`` now sends more persuasive
  headers (added ``no-cache, no-store, must-revalidate`` to ``Cache-Control``)
  to better prevent caching (:ticket:`13008`). This fixes a problem where a
  page refresh in Firefox cleared the selected entries in the admin's
  ``filter_horizontal`` and ``filter_vertical`` widgets, which could result
  in inadvertent data loss if a user didn't notice that and then submitted the
  form (:ticket:`22955`).

* Fixed a regression in the admin which ignored line breaks in read-only fields
  instead of converting them to ``<br>`` (:ticket:`25465`).

* Made ``loaddata`` skip disabling and enabling database constraints when it
  doesn't load any fixtures (:ticket:`23372`).

* Fixed a crash in ``QuerySet.values()/values_list()`` after an ``annotate()``
  and ``order_by()`` when ``values()/values_list()`` includes a field not in
  the ``order_by()`` (:ticket:`25316`).
