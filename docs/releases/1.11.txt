=========================
Django 1.11 release notes
=========================

*April 4, 2017*

Welcome to Django 1.11!

These release notes cover the :ref:`new features <whats-new-1.11>`, as well as
some :ref:`backwards incompatible changes <backwards-incompatible-1.11>` you'll
want to be aware of when upgrading from Django 1.10 or older versions. We've
:ref:`begun the deprecation process for some features
<deprecated-features-1.11>`.

See the :doc:`/howto/upgrade-version` guide if you're updating an existing
project.

Django 1.11 is designated as a :term:`long-term support release
<Long-term support release>`. It will receive security updates for at least
three years after its release. Support for the previous LTS, Django 1.8, will
end in April 2018.

Python compatibility
====================

Django 1.11 requires Python 2.7, 3.4, 3.5, 3.6, or 3.7 (as of 1.11.17). We
**highly recommend** and only officially support the latest release of each
series.

The Django 1.11.x series is the last to support Python 2. The next major
release, Django 2.0, will only support Python 3.4+.

Deprecating warnings are no longer loud by default
==================================================

Unlike older versions of Django, Django's own deprecation warnings are no
longer displayed by default. This is consistent with Python's default behavior.

This change allows third-party apps to support both Django 1.11 LTS and Django
1.8 LTS without having to add code to avoid deprecation warnings.

Following the release of Django 2.0, we suggest that third-party app authors
drop support for all versions of Django prior to 1.11. At that time, you should
be able run your package's tests using ``python -Wd`` so that deprecation
warnings do appear. After making the deprecation warning fixes, your app should
be compatible with Django 2.0.

.. _whats-new-1.11:

What's new in Django 1.11
=========================

Class-based model indexes
-------------------------

The new :mod:`django.db.models.indexes` module contains classes which ease
creating database indexes. Indexes are added to models using the
:attr:`Meta.indexes <django.db.models.Options.indexes>` option.

The :class:`~django.db.models.Index` class creates a b-tree index, as if you
used :attr:`~django.db.models.Field.db_index` on the model field or
:attr:`~django.db.models.Options.index_together` on the model ``Meta`` class.
It can be subclassed to support different index types, such as
:class:`~django.contrib.postgres.indexes.GinIndex`. It also allows defining the
order (ASC/DESC) for the columns of the index.

Template-based widget rendering
-------------------------------

To ease customizing widgets, form widget rendering is now done using the
template system rather than in Python. See :doc:`/ref/forms/renderers`.

You may need to adjust any custom widgets that you've written for a few
:ref:`backwards incompatible changes <template-widget-incompatibilities-1-11>`.

``Subquery`` expressions
------------------------

The new :class:`~django.db.models.Subquery` and
:class:`~django.db.models.Exists` database expressions allow creating
explicit subqueries. Subqueries may refer to fields from the outer queryset
using the :class:`~django.db.models.OuterRef` class.

Minor features
--------------

:mod:`django.contrib.admin`
~~~~~~~~~~~~~~~~~~~~~~~~~~~

* :attr:`.ModelAdmin.date_hierarchy` can now reference fields across relations.

* The new :meth:`ModelAdmin.get_exclude()
  <django.contrib.admin.ModelAdmin.get_exclude>` hook allows specifying the
  exclude fields based on the request or model instance.

* The ``popup_response.html`` template can now be overridden per app, per
  model, or by setting the :attr:`.ModelAdmin.popup_response_template`
  attribute.

:mod:`django.contrib.auth`
~~~~~~~~~~~~~~~~~~~~~~~~~~

* The default iteration count for the PBKDF2 password hasher is increased by
  20%.

* The :class:`~django.contrib.auth.views.LoginView` and
  :class:`~django.contrib.auth.views.LogoutView` class-based views supersede the
  deprecated ``login()`` and ``logout()`` function-based views.

* The :class:`~django.contrib.auth.views.PasswordChangeView`,
  :class:`~django.contrib.auth.views.PasswordChangeDoneView`,
  :class:`~django.contrib.auth.views.PasswordResetView`,
  :class:`~django.contrib.auth.views.PasswordResetDoneView`,
  :class:`~django.contrib.auth.views.PasswordResetConfirmView`, and
  :class:`~django.contrib.auth.views.PasswordResetCompleteView` class-based
  views supersede the deprecated ``password_change()``,
  ``password_change_done()``, ``password_reset()``, ``password_reset_done()``,
  ``password_reset_confirm()``, and ``password_reset_complete()`` function-based
  views.

* The new ``post_reset_login`` attribute for
  :class:`~django.contrib.auth.views.PasswordResetConfirmView` allows
  automatically logging in a user after a successful password reset.
  If you have multiple ``AUTHENTICATION_BACKENDS`` configured, use the
  ``post_reset_login_backend`` attribute to choose which one to use.

* To avoid the possibility of leaking a password reset token via the HTTP
  Referer header (for example, if the reset page includes a reference to CSS or
  JavaScript hosted on another domain), the
  :class:`~django.contrib.auth.views.PasswordResetConfirmView` (but not the
  deprecated ``password_reset_confirm()`` function-based view) stores the token
  in a session and redirects to itself to present the password change form to
  the user without the token in the URL.

* :func:`~django.contrib.auth.update_session_auth_hash` now rotates the session
  key to allow a password change to invalidate stolen session cookies.

* The new ``success_url_allowed_hosts`` attribute for
  :class:`~django.contrib.auth.views.LoginView` and
  :class:`~django.contrib.auth.views.LogoutView` allows specifying a set of
  hosts that are safe for redirecting after login and logout.

* Added password validators ``help_text`` to
  :class:`~django.contrib.auth.forms.UserCreationForm`.

* The ``HttpRequest`` is now passed to :func:`~django.contrib.auth.authenticate`
  which in turn passes it to the authentication backend if it accepts a
  ``request`` argument.

* The :func:`~django.contrib.auth.signals.user_login_failed` signal now
  receives a ``request`` argument.

* :class:`~django.contrib.auth.forms.PasswordResetForm` supports custom user
  models that use an email field named something other than ``'email'``.
  Set :attr:`CustomUser.EMAIL_FIELD
  <django.contrib.auth.models.CustomUser.EMAIL_FIELD>` to the name of the field.

* :func:`~django.contrib.auth.get_user_model` can now be called at import time,
  even in modules that define models.

:mod:`django.contrib.contenttypes`
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

* When stale content types are detected in the
  :djadmin:`remove_stale_contenttypes` command, there's now a list of related
  objects such as ``auth.Permission``\s that will also be deleted. Previously,
  only the content types were listed (and this prompt was after ``migrate``
  rather than in a separate command).

:mod:`django.contrib.gis`
~~~~~~~~~~~~~~~~~~~~~~~~~

* The new :meth:`.GEOSGeometry.from_gml` and :meth:`.OGRGeometry.from_gml`
  methods allow creating geometries from GML.

* Added support for the :lookup:`dwithin` lookup on SpatiaLite.

* The :class:`~django.contrib.gis.db.models.functions.Area` function,
  :class:`~django.contrib.gis.db.models.functions.Distance` function, and
  distance lookups now work with geodetic coordinates on SpatiaLite.

* The OpenLayers-based form widgets now use ``OpenLayers.js`` from
  ``https://cdnjs.cloudflare.com`` which is more suitable for production use
  than the old ``https://openlayers.org/`` source. They are also updated to use
  OpenLayers 3.

* PostGIS migrations can now change field dimensions.

* Added the ability to pass the ``size``, ``shape``, and ``offset`` parameters
  when creating :class:`~django.contrib.gis.gdal.GDALRaster` objects.

* Added SpatiaLite support for the
  :class:`~django.contrib.gis.db.models.functions.IsValid` function,
  :class:`~django.contrib.gis.db.models.functions.MakeValid` function, and
  :lookup:`isvalid` lookup.

* Added Oracle support for the
  :class:`~django.contrib.gis.db.models.functions.AsGML` function,
  :class:`~django.contrib.gis.db.models.functions.BoundingCircle` function,
  :class:`~django.contrib.gis.db.models.functions.IsValid` function, and
  :lookup:`isvalid` lookup.

:mod:`django.contrib.postgres`
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

* The new ``distinct`` argument for
  :class:`~django.contrib.postgres.aggregates.StringAgg` determines if
  concatenated values will be distinct.

* The new :class:`~django.contrib.postgres.indexes.GinIndex` and
  :class:`~django.contrib.postgres.indexes.BrinIndex` classes allow
  creating ``GIN`` and ``BRIN`` indexes in the database.

* ``django.contrib.postgres.fields.JSONField`` accepts a new ``encoder``
  parameter to specify a custom class to encode data types not supported by the
  standard encoder.

* The new ``CIText`` mixin and
  :class:`~django.contrib.postgres.operations.CITextExtension` migration
  operation allow using PostgreSQL's ``citext`` extension for case-insensitive
  lookups. Three fields are provided: ``CICharField``, ``CIEmailField``, and
  ``CITextField``.

* The new :class:`~django.contrib.postgres.aggregates.JSONBAgg` allows
  aggregating values as a JSON array.

* The :class:`~django.contrib.postgres.fields.HStoreField` (model field) and
  :class:`~django.contrib.postgres.forms.HStoreField` (form field) allow
  storing null values.

Cache
~~~~~

* Memcached backends now pass the contents of :setting:`OPTIONS <CACHES-OPTIONS>`
  as keyword arguments to the client constructors, allowing for more advanced
  control of client behavior. See the :ref:`cache arguments <cache_arguments>`
  documentation for examples.

* Memcached backends now allow defining multiple servers as a comma-delimited
  string in :setting:`LOCATION <CACHES-LOCATION>`, for convenience with
  third-party services that use such strings in environment variables.

CSRF
~~~~

* Added the :setting:`CSRF_USE_SESSIONS` setting to allow storing the CSRF
  token in the user's session rather than in a cookie.

Database backends
~~~~~~~~~~~~~~~~~

* Added the ``skip_locked`` argument to :meth:`.QuerySet.select_for_update()`
  on PostgreSQL 9.5+ and Oracle to execute queries with
  ``FOR UPDATE SKIP LOCKED``.

* Added the :setting:`TEST['TEMPLATE'] <TEST_TEMPLATE>` setting to let
  PostgreSQL users specify a template for creating the test database.

* :meth:`.QuerySet.iterator()` now uses `server-side cursors`_ on PostgreSQL.
  This feature transfers some of the worker memory load (used to hold query
  results) to the database and might increase database memory usage.

  .. _server-side cursors: https://www.psycopg.org/docs/usage.html#server-side-cursors

* Added MySQL support for the ``'isolation_level'`` option in
  :setting:`OPTIONS` to allow specifying the :ref:`transaction isolation level
  <mysql-isolation-level>`. To avoid possible data loss, it's recommended to
  switch from MySQL's default level, repeatable read, to read committed.

* Added support for ``cx_Oracle`` 5.3.

Email
~~~~~

* Added the :setting:`EMAIL_USE_LOCALTIME` setting to allow sending SMTP date
  headers in the local time zone rather than in UTC.

* ``EmailMessage.attach()`` and ``attach_file()`` now fall back to MIME type
  :mimetype:`application/octet-stream` when binary content that can't be
  decoded as UTF-8 is specified for a :mimetype:`text/*` attachment.

File Storage
~~~~~~~~~~~~

* To make it wrappable by :class:`io.TextIOWrapper`,
  :class:`~django.core.files.File` now has the ``readable()``, ``writable()``,
  and ``seekable()`` methods.

Forms
~~~~~

* The new ``empty_value`` attribute on :class:`~django.forms.CharField`,
  :class:`~django.forms.EmailField`, :class:`~django.forms.RegexField`,
  :class:`~django.forms.SlugField`, and :class:`~django.forms.URLField` allows
  specifying the Python value to use to represent "empty".

* The new :meth:`Form.get_initial_for_field()
  <django.forms.Form.get_initial_for_field>` method returns initial data for a
  form field.

Internationalization
~~~~~~~~~~~~~~~~~~~~

* Number formatting and the :setting:`NUMBER_GROUPING` setting support
  non-uniform digit grouping.

Management Commands
~~~~~~~~~~~~~~~~~~~

* The new :option:`loaddata --exclude` option allows excluding models and apps
  while loading data from fixtures.

* The new :option:`diffsettings --default` option allows specifying a settings
  module other than Django's default settings to compare against.

* ``app_label``\s arguments now limit the :option:`showmigrations --plan`
  output.

Migrations
~~~~~~~~~~

* Added support for serialization of ``uuid.UUID`` objects.

Models
~~~~~~

* Added support for callable values in the ``defaults`` argument of
  :meth:`QuerySet.update_or_create()
  <django.db.models.query.QuerySet.update_or_create>` and
  :meth:`~django.db.models.query.QuerySet.get_or_create`.

* :class:`~django.db.models.ImageField` now has a default
  :data:`~django.core.validators.validate_image_file_extension` validator.
  (This validator moved to the form field in :doc:`Django 1.11.2 <1.11.2>`.)

* Added support for time truncation to
  :class:`~django.db.models.functions.Trunc` functions.

* Added the :class:`~django.db.models.functions.ExtractWeek` function to
  extract the week from :class:`~django.db.models.DateField` and
  :class:`~django.db.models.DateTimeField` and exposed it through the
  :lookup:`week` lookup.

* Added the :class:`~django.db.models.functions.TruncTime` function to truncate
  :class:`~django.db.models.DateTimeField` to its time component and exposed it
  through the :lookup:`time` lookup.

* Added support for expressions in :meth:`.QuerySet.values` and
  :meth:`~.QuerySet.values_list`.

* Added support for query expressions on lookups that take multiple arguments,
  such as ``range``.

* You can now use the ``unique=True`` option with
  :class:`~django.db.models.FileField`.

* Added the ``nulls_first`` and ``nulls_last`` parameters to
  :class:`Expression.asc() <django.db.models.Expression.asc>` and
  :meth:`~django.db.models.Expression.desc` to control
  the ordering of null values.

* The new ``F`` expression ``bitleftshift()`` and ``bitrightshift()`` methods
  allow :ref:`bitwise shift operations <using-f-expressions-in-filters>`.

* Added :meth:`.QuerySet.union`, :meth:`~.QuerySet.intersection`, and
  :meth:`~.QuerySet.difference`.

Requests and Responses
~~~~~~~~~~~~~~~~~~~~~~

* Added :meth:`QueryDict.fromkeys() <django.http.QueryDict.fromkeys>`.

* :class:`~django.middleware.common.CommonMiddleware` now sets the
  ``Content-Length`` response header for non-streaming responses.

* Added the :setting:`SECURE_HSTS_PRELOAD` setting to allow appending the
  ``preload`` directive to the ``Strict-Transport-Security`` header.

* :class:`~django.middleware.http.ConditionalGetMiddleware` now adds the
  ``ETag`` header to responses.

Serialization
~~~~~~~~~~~~~

* The new ``django.core.serializers.base.Serializer.stream_class`` attribute
  allows subclasses to customize the default stream.

* The encoder used by the :ref:`JSON serializer <serialization-formats-json>`
  can now be customized by passing a ``cls`` keyword argument to the
  ``serializers.serialize()`` function.

* :class:`~django.core.serializers.json.DjangoJSONEncoder` now serializes
  :class:`~datetime.timedelta` objects (used by
  :class:`~django.db.models.DurationField`).

Templates
~~~~~~~~~

* :meth:`~django.utils.safestring.mark_safe` can now be used as a decorator.

* The :class:`~django.template.backends.jinja2.Jinja2` template backend now
  supports context processors by setting the ``'context_processors'`` option in
  :setting:`OPTIONS <TEMPLATES-OPTIONS>`.

* The :ttag:`regroup` tag now returns ``namedtuple``\s instead of dictionaries
  so you can unpack the group object directly in a loop, e.g.
  ``{% for grouper, list in regrouped %}``.

* Added a :ttag:`resetcycle` template tag to allow resetting the sequence of
  the :ttag:`cycle` template tag.

* You can now specify specific directories for a particular
  :class:`filesystem.Loader <django.template.loaders.filesystem.Loader>`.

Tests
~~~~~

* Added :meth:`.DiscoverRunner.get_test_runner_kwargs` to allow customizing the
  keyword arguments passed to the test runner.

* Added the :option:`test --debug-mode` option to help troubleshoot test
  failures by setting the :setting:`DEBUG` setting to ``True``.

* The new :func:`django.test.utils.setup_databases` (moved from
  ``django.test.runner``) and :func:`~django.test.utils.teardown_databases`
  functions make it easier to build custom test runners.

* Added support for :meth:`python:unittest.TestCase.subTest`’s when using the
  :option:`test --parallel` option.

* ``DiscoverRunner`` now runs the system checks at the start of a test run.
  Override the :meth:`.DiscoverRunner.run_checks` method if you want to disable
  that.

Validators
~~~~~~~~~~

* Added :class:`~django.core.validators.FileExtensionValidator` to validate
  file extensions and
  :data:`~django.core.validators.validate_image_file_extension` to validate
  image files.

.. _backwards-incompatible-1.11:

Backwards incompatible changes in 1.11
======================================

:mod:`django.contrib.gis`
-------------------------

* To simplify the codebase and because it's easier to install than when
  ``contrib.gis`` was first released, :ref:`gdalbuild` is now a required
  dependency for GeoDjango. In older versions, it's only required for SQLite.

* ``contrib.gis.maps`` is removed as it interfaces with a retired version of
  the Google Maps API and seems to be unmaintained. If you're using it,
  :ticket:`let us know <14284>`.

* The ``GEOSGeometry`` equality operator now also compares SRID.

* The OpenLayers-based form widgets now use OpenLayers 3, and the
  ``gis/openlayers.html`` and ``gis/openlayers-osm.html`` templates have been
  updated. Check your project if you subclass these widgets or extend the
  templates. Also, the new widgets work a bit differently than the old ones.
  Instead of using a toolbar in the widget, you click to draw, click and drag
  to move the map, and click and drag a point/vertex/corner to move it.

* Support for SpatiaLite < 4.0 is dropped.

* Support for GDAL 1.7 and 1.8 is dropped.

* The widgets in ``contrib.gis.forms.widgets`` and the admin's
  ``OpenLayersWidget`` use the :doc:`form rendering API </ref/forms/renderers>`
  rather than ``loader.render_to_string()``. If you're using a custom widget
  template, you'll need to be sure your form renderer can locate it. For
  example, you could use the :class:`~django.forms.renderers.TemplatesSetting`
  renderer.

:mod:`django.contrib.staticfiles`
---------------------------------

* ``collectstatic`` may now fail during post-processing when using a hashed
  static files storage if a reference loop exists (e.g. ``'foo.css'``
  references ``'bar.css'`` which itself references ``'foo.css'``) or if the
  chain of files referencing other files is too deep to resolve in several
  passes. In the latter case, increase the number of passes using
  :attr:`.ManifestStaticFilesStorage.max_post_process_passes`.

* When using ``ManifestStaticFilesStorage``, static files not found in the
  manifest at runtime now raise a ``ValueError`` instead of returning an
  unchanged path. You can revert to the old behavior by setting
  :attr:`.ManifestStaticFilesStorage.manifest_strict` to ``False``.

Database backend API
--------------------

This section describes changes that may be needed in third-party database
backends.

* The ``DatabaseOperations.time_trunc_sql()`` method is added to support
  ``TimeField`` truncation. It accepts a ``lookup_type`` and ``field_name``
  arguments and returns the appropriate SQL to truncate the given time field
  ``field_name`` to a time object with only the given specificity. The
  ``lookup_type`` argument can be either ``'hour'``, ``'minute'``, or
  ``'second'``.

* The ``DatabaseOperations.datetime_cast_time_sql()`` method is added to
  support the :lookup:`time` lookup. It accepts a ``field_name`` and ``tzname``
  arguments and returns the SQL necessary to cast a datetime value to time value.

* To enable ``FOR UPDATE SKIP LOCKED`` support, set
  ``DatabaseFeatures.has_select_for_update_skip_locked = True``.

* The new ``DatabaseFeatures.supports_index_column_ordering`` attribute
  specifies if a database allows defining ordering for columns in indexes. The
  default value is ``True`` and the ``DatabaseIntrospection.get_constraints()``
  method should include an ``'orders'`` key in each of the returned
  dictionaries with a list of ``'ASC'`` and/or ``'DESC'`` values corresponding
  to the ordering of each column in the index.

* :djadmin:`inspectdb` no longer calls ``DatabaseIntrospection.get_indexes()``
  which is deprecated. Custom database backends should ensure all types of
  indexes are returned by ``DatabaseIntrospection.get_constraints()``.

* Renamed the ``ignores_quoted_identifier_case`` feature to
  ``ignores_table_name_case`` to more accurately reflect how it is used.

* The ``name`` keyword argument is added to the
  ``DatabaseWrapper.create_cursor(self, name=None)`` method to allow usage of
  server-side cursors on backends that support it.

Dropped support for PostgreSQL 9.2 and PostGIS 2.0
--------------------------------------------------

Upstream support for PostgreSQL 9.2 ends in September 2017. As a consequence,
Django 1.11 sets PostgreSQL 9.3 as the minimum version it officially supports.

Support for PostGIS 2.0 is also removed as PostgreSQL 9.2 is the last version
to support it.

Also, the minimum supported version of psycopg2 is increased from 2.4.5 to
2.5.4.

.. _liveservertestcase-port-zero-change:

``LiveServerTestCase`` binds to port zero
-----------------------------------------

Rather than taking a port range and iterating to find a free port,
``LiveServerTestCase`` binds to port zero and relies on the operating system
to assign a free port. The ``DJANGO_LIVE_TEST_SERVER_ADDRESS`` environment
variable is no longer used, and as it's also no longer used, the
``manage.py test --liveserver`` option is removed.

If you need to bind ``LiveServerTestCase`` to a specific port, use the ``port``
attribute added in Django 1.11.2.

Protection against insecure redirects in :mod:`django.contrib.auth` and ``i18n`` views
--------------------------------------------------------------------------------------

``LoginView``, ``LogoutView`` (and the deprecated function-based equivalents),
and :func:`~django.views.i18n.set_language` protect users from being redirected
to non-HTTPS ``next`` URLs when the app is running over HTTPS.

``QuerySet.get_or_create()`` and ``update_or_create()`` validate arguments
--------------------------------------------------------------------------

To prevent typos from passing silently,
:meth:`~django.db.models.query.QuerySet.get_or_create` and
:meth:`~django.db.models.query.QuerySet.update_or_create` check that their
arguments are model fields. This should be backwards-incompatible only in the
fact that it might expose a bug in your project.

``pytz`` is a required dependency and support for ``settings.TIME_ZONE = None`` is removed
------------------------------------------------------------------------------------------

To simplify Django's timezone handling, ``pytz`` is now a required dependency.
It's automatically installed along with Django.

Support for ``settings.TIME_ZONE = None`` is removed as the behavior isn't
commonly used and is questionably useful. If you want to automatically detect
the timezone based on the system timezone, you can use :pypi:`tzlocal`::

    from tzlocal import get_localzone

    TIME_ZONE = get_localzone().zone

This works similar to ``settings.TIME_ZONE = None`` except that it also sets
``os.environ['TZ']``. `Let us know
<https://groups.google.com/g/django-developers/c/OAV3FChfuPM/discussion>`__
if there's a use case where you find you can't adapt your code to set a
``TIME_ZONE``.

HTML changes in admin templates
-------------------------------

``<p class="help">`` is replaced with a ``<div>`` tag to allow including lists
inside help text.

Read-only fields are wrapped in ``<div class="readonly">...</div>`` instead of
``<p>...</p>`` to allow any kind of HTML as the field's content.

.. _template-widget-incompatibilities-1-11:

Changes due to the introduction of template-based widget rendering
------------------------------------------------------------------

Some undocumented classes in ``django.forms.widgets`` are removed:

* ``SubWidget``
* ``RendererMixin``, ``ChoiceFieldRenderer``, ``RadioFieldRenderer``,
  ``CheckboxFieldRenderer``
* ``ChoiceInput``, ``RadioChoiceInput``, ``CheckboxChoiceInput``

The undocumented ``Select.render_option()`` method is removed.

The ``Widget.format_output()`` method is removed. Use a custom widget template
instead.

Some widget values, such as ``<select>`` options, are now localized if
``settings.USE_L10N=True``. You could revert to the old behavior with custom
widget templates that uses the :ttag:`localize` template tag to turn off
localization.

``django.template.backends.django.Template.render()`` prohibits non-dict context
--------------------------------------------------------------------------------

For compatibility with multiple template engines,
``django.template.backends.django.Template.render()`` (returned from high-level
template loader APIs such as ``loader.get_template()``) must receive a
dictionary of context rather than ``Context`` or ``RequestContext``. If you
were passing either of the two classes, pass a dictionary instead -- doing so
is backwards-compatible with older versions of Django.

Model state changes in migration operations
-------------------------------------------

To improve the speed of applying migrations, rendering of related models is
delayed until an operation that needs them (e.g. ``RunPython``). If you have a
custom operation that works with model classes or model instances from the
``from_state`` argument in ``database_forwards()`` or ``database_backwards()``,
you must render model states using the ``clear_delayed_apps_cache()`` method as
described in :ref:`writing your own migration operation
<writing-your-own-migration-operation>`.

Server-side cursors on PostgreSQL
---------------------------------

The change to make :meth:`.QuerySet.iterator()` use server-side cursors on
PostgreSQL prevents running Django with PgBouncer in transaction pooling mode.
To reallow that, use the :setting:`DISABLE_SERVER_SIDE_CURSORS
<DATABASE-DISABLE_SERVER_SIDE_CURSORS>` setting (added in Django 1.11.1) in
:setting:`DATABASES`.

See :ref:`transaction-pooling-server-side-cursors` for more discussion.

Miscellaneous
-------------

* If no items in the feed have a ``pubdate`` or ``updateddate`` attribute,
  :meth:`SyndicationFeed.latest_post_date()
  <django.utils.feedgenerator.SyndicationFeed.latest_post_date>` now returns
  the current UTC date/time, instead of a datetime without any timezone
  information.

* CSRF failures are logged to the ``django.security.csrf`` logger instead of
  ``django.request``.

* :setting:`ALLOWED_HOSTS` validation is no longer disabled when running tests.
  If your application includes tests with custom host names, you must include
  those host names in :setting:`ALLOWED_HOSTS`. See
  :ref:`topics-testing-advanced-multiple-hosts`.

* Using a foreign key's id (e.g. ``'field_id'``) in ``ModelAdmin.list_display``
  displays the related object's ID. Remove the ``_id`` suffix if you want the
  old behavior of the string representation of the object.

* In model forms, :class:`~django.db.models.CharField` with ``null=True`` now
  saves ``NULL`` for blank values instead of empty strings.

* On Oracle, :meth:`Model.validate_unique()
  <django.db.models.Model.validate_unique>` no longer checks empty strings for
  uniqueness as the database interprets the value as ``NULL``.

* If you subclass :class:`.AbstractUser` and override ``clean()``, be sure it
  calls ``super()``. :meth:`.BaseUserManager.normalize_email` is called in a
  new :meth:`.AbstractUser.clean` method so that normalization is applied in
  cases like model form validation.

* ``EmailField`` and ``URLField`` no longer accept the ``strip`` keyword
  argument. Remove it because it doesn't have an effect in older versions of
  Django as these fields always strip whitespace.

* The ``checked`` and ``selected`` attribute rendered by form widgets now uses
  HTML5 boolean syntax rather than XHTML's ``checked='checked'`` and
  ``selected='selected'``.

* :meth:`RelatedManager.add()
  <django.db.models.fields.related.RelatedManager.add>`,
  :meth:`~django.db.models.fields.related.RelatedManager.remove`,
  :meth:`~django.db.models.fields.related.RelatedManager.clear`, and
  :meth:`~django.db.models.fields.related.RelatedManager.set` now
  clear the ``prefetch_related()`` cache.

* To prevent possible loss of saved settings,
  :func:`~django.test.utils.setup_test_environment` now raises an exception if
  called a second time before calling
  :func:`~django.test.utils.teardown_test_environment`.

* The undocumented ``DateTimeAwareJSONEncoder`` alias for
  :class:`~django.core.serializers.json.DjangoJSONEncoder` (renamed in Django
  1.0) is removed.

* The :class:`cached template loader <django.template.loaders.cached.Loader>`
  is now enabled if :setting:`OPTIONS['loaders'] <TEMPLATES-OPTIONS>` isn't
  specified and :setting:`OPTIONS['debug'] <TEMPLATES-OPTIONS>` is ``False``
  (the latter option defaults to the value of :setting:`DEBUG`). This could
  be backwards-incompatible if you have some :ref:`template tags that aren't
  thread safe <template_tag_thread_safety>`.

* The prompt for stale content type deletion no longer occurs after running the
  ``migrate`` command. Use the new :djadmin:`remove_stale_contenttypes` command
  instead.

* The admin's widget for ``IntegerField`` uses ``type="number"`` rather than
  ``type="text"``.

* Conditional HTTP headers are now parsed and compared according to the
  :rfc:`7232` Conditional Requests specification rather than the older
  :rfc:`2616`.

* :func:`~django.utils.cache.patch_response_headers` no longer adds a
  ``Last-Modified`` header. According to the :rfc:`7234#section-4.2.2`, this
  header is useless alongside other caching headers that provide an explicit
  expiration time, e.g. ``Expires`` or ``Cache-Control``.
  :class:`~django.middleware.cache.UpdateCacheMiddleware` and
  :func:`~django.utils.cache.add_never_cache_headers` call
  ``patch_response_headers()`` and therefore are also affected by this change.

* In the admin templates, ``<p class="help">`` is replaced with a ``<div>`` tag
  to allow including lists inside help text.

* :class:`~django.middleware.http.ConditionalGetMiddleware` no longer sets the
  ``Date`` header as web servers set that header. It also no longer sets the
  ``Content-Length`` header as this is now done by
  :class:`~django.middleware.common.CommonMiddleware`.

  If you have a middleware that modifies a response's content and appears
  before ``CommonMiddleware`` in the ``MIDDLEWARE`` or ``MIDDLEWARE_CLASSES``
  settings, you must reorder your middleware so that responses aren't modified
  after ``Content-Length`` is set, or have the response modifying middleware
  reset the ``Content-Length`` header.

* :meth:`~django.apps.AppConfig.get_model` and
  :meth:`~django.apps.AppConfig.get_models` now raise
  :exc:`~django.core.exceptions.AppRegistryNotReady` if they're called before
  models of all applications have been loaded. Previously they only required
  the target application's models to be loaded and thus could return models
  without all their relations set up. If you need the old behavior of
  ``get_model()``, set the ``require_ready`` argument to ``False``.

* The unused ``BaseCommand.can_import_settings`` attribute is removed.

* The undocumented ``django.utils.functional.lazy_property`` is removed.

* For consistency with non-multipart requests, ``MultiPartParser.parse()`` now
  leaves ``request.POST`` immutable. If you're modifying that ``QueryDict``,
  you must now first copy it, e.g. ``request.POST.copy()``.

* Support for ``cx_Oracle`` < 5.2 is removed.

* Support for IPython < 1.0 is removed from the ``shell`` command.

* The signature of private API ``Widget.build_attrs()`` changed from
  ``extra_attrs=None, **kwargs`` to ``base_attrs, extra_attrs=None``.

* File-like objects (e.g., :class:`~io.StringIO` and :class:`~io.BytesIO`)
  uploaded to an :class:`~django.db.models.ImageField` using the test client
  now require a ``name`` attribute with a value that passes the
  :data:`~django.core.validators.validate_image_file_extension` validator.
  See the note in :meth:`.Client.post`.

* :class:`~django.db.models.FileField` now moves rather than copies the file
  it receives. With the default file upload settings, files larger than
  :setting:`FILE_UPLOAD_MAX_MEMORY_SIZE` now have the same permissions as
  temporary files (often ``0o600``) rather than the system's standard umask
  (often ``0o6644``). Set the :setting:`FILE_UPLOAD_PERMISSIONS` if you need
  the same permission regardless of file size.

.. _deprecated-features-1.11:

Features deprecated in 1.11
===========================

``models.permalink()`` decorator
--------------------------------

Use :func:`django.urls.reverse` instead. For example::

    from django.db import models


    class MyModel(models.Model):
        ...

        @models.permalink
        def url(self):
            return ("guitarist_detail", [self.slug])

becomes::

    from django.db import models
    from django.urls import reverse


    class MyModel(models.Model):
        ...

        def url(self):
            return reverse("guitarist_detail", args=[self.slug])

Miscellaneous
-------------

* ``contrib.auth``’s ``login()`` and ``logout()`` function-based views are
  deprecated in favor of new class-based views
  :class:`~django.contrib.auth.views.LoginView` and
  :class:`~django.contrib.auth.views.LogoutView`.

* The unused ``extra_context`` parameter of
  ``contrib.auth.views.logout_then_login()``  is deprecated.

* ``contrib.auth``’s ``password_change()``, ``password_change_done()``,
  ``password_reset()``, ``password_reset_done()``, ``password_reset_confirm()``,
  and ``password_reset_complete()`` function-based views are deprecated in favor
  of new class-based views
  :class:`~django.contrib.auth.views.PasswordChangeView`,
  :class:`~django.contrib.auth.views.PasswordChangeDoneView`,
  :class:`~django.contrib.auth.views.PasswordResetView`,
  :class:`~django.contrib.auth.views.PasswordResetDoneView`,
  :class:`~django.contrib.auth.views.PasswordResetConfirmView`, and
  :class:`~django.contrib.auth.views.PasswordResetCompleteView`.

* ``django.test.runner.setup_databases()`` is moved to
  :func:`django.test.utils.setup_databases`. The old location is deprecated.

* ``django.utils.translation.string_concat()`` is deprecated in
  favor of :func:`django.utils.text.format_lazy`. ``string_concat(*strings)``
  can be replaced by ``format_lazy('{}' * len(strings), *strings)``.

* For the ``PyLibMCCache`` cache backend, passing ``pylibmc`` behavior settings
  as top-level attributes of ``OPTIONS`` is deprecated. Set them under a
  ``behaviors`` key within ``OPTIONS`` instead.

* The ``host`` parameter of ``django.utils.http.is_safe_url()`` is deprecated
  in favor of the new ``allowed_hosts`` parameter.

* Silencing exceptions raised while rendering the
  :ttag:`{% include %} <include>` template tag is deprecated as the behavior is
  often more confusing than helpful. In Django 2.1, the exception will be
  raised.

* ``DatabaseIntrospection.get_indexes()`` is deprecated in favor of
  ``DatabaseIntrospection.get_constraints()``.

* :func:`~django.contrib.auth.authenticate` now passes a ``request`` argument
  to the ``authenticate()`` method of authentication backends. Support for
  methods that don't accept ``request`` as the first positional argument will
  be removed in Django 2.1.

* The ``USE_ETAGS`` setting is deprecated in favor of
  :class:`~django.middleware.http.ConditionalGetMiddleware` which now adds the
  ``ETag`` header to responses regardless of the setting. ``CommonMiddleware``
  and ``django.utils.cache.patch_response_headers()`` will no longer set ETags
  when the deprecation ends.

* ``Model._meta.has_auto_field`` is deprecated in favor of checking if
  ``Model._meta.auto_field is not None``.

* Using regular expression groups with ``iLmsu#`` in ``url()`` is deprecated.
  The only group that's useful is ``(?i)`` for case-insensitive URLs, however,
  case-insensitive URLs aren't a good practice because they create multiple
  entries for search engines, for example. An alternative solution could be to
  create a :data:`~django.conf.urls.handler404` that looks for uppercase
  characters in the URL and redirects to a lowercase equivalent.

* The ``renderer`` argument is added to the :meth:`Widget.render()
  <django.forms.Widget.render>` method. Methods that don't accept that argument
  will work through a deprecation period.
