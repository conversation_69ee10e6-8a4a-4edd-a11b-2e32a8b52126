==========================
Django 1.3.4 release notes
==========================

*October 17, 2012*

This is the fourth release in the Django 1.3 series.

Host header poisoning
=====================

Some parts of Django -- independent of end-user-written applications -- make
use of full URLs, including domain name, which are generated from the HTTP Host
header. Some attacks against this are beyond Django's ability to control, and
require the web server to be properly configured; Django's documentation has
for some time contained notes advising users on such configuration.

Django's own built-in parsing of the Host header is, however, still vulnerable,
as was reported to us recently. The Host header parsing in Django 1.3.3 and
Django 1.4.1 -- specifically, ``django.http.HttpRequest.get_host()`` -- was
incorrectly handling username/password information in the header. Thus, for
example, the following Host header would be accepted by Django when running on
``validsite.com``:

.. code-block:: text

    Host: validsite.com:<EMAIL>

Using this, an attacker can cause parts of Django -- particularly the
password-reset mechanism -- to generate and display arbitrary URLs to users.

To remedy this, the parsing in ``HttpRequest.get_host()`` is being modified;
Host headers which contain potentially dangerous content (such as
username/password pairs) now raise the exception
:exc:`django.core.exceptions.SuspiciousOperation`.

Details of this issue were initially posted online as a `security advisory`_.

.. _security advisory: https://www.djangoproject.com/weblog/2012/oct/17/security/
