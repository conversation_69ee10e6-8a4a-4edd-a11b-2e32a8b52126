==========================
Django 1.7.7 release notes
==========================

*March 18, 2015*

Django 1.7.7 fixes several bugs and security issues in 1.7.6.

Denial-of-service possibility with ``strip_tags()``
===================================================

Last year :func:`~django.utils.html.strip_tags`  was changed to work
iteratively. The problem is that the size of the input it's processing can
increase on each iteration which results in an infinite loop in
``strip_tags()``. This issue only affects versions of Python that haven't
received :bpo:`a bugfix in HTMLParser <20288>`; namely Python < 2.7.7 and
3.3.5. Some operating system vendors have also backported the fix for the
Python bug into their packages of earlier versions.

To remedy this issue, ``strip_tags()`` will now return the original input if
it detects the length of the string it's processing increases. Remember that
absolutely NO guarantee is provided about the results of ``strip_tags()`` being
HTML safe. So NEVER mark safe the result of a ``strip_tags()`` call without
escaping it first, for example with :func:`~django.utils.html.escape`.

Mitigated possible XSS attack via user-supplied redirect URLs
=============================================================

Django relies on user input in some cases (e.g.
``django.contrib.auth.views.login()`` and :doc:`i18n </topics/i18n/index>`)
to redirect the user to an "on success" URL. The security checks for these
redirects (namely ``django.utils.http.is_safe_url()``) accepted URLs with
leading control characters and so considered URLs like ``\x08javascript:...``
safe. This issue doesn't affect Django currently, since we only put this URL
into the ``Location`` response header and browsers seem to ignore JavaScript
there. Browsers we tested also treat URLs prefixed with control characters such
as ``%08//example.com`` as relative paths so redirection to an unsafe target
isn't a problem either.

However, if a developer relies on ``is_safe_url()`` to
provide safe redirect targets and puts such a URL into a link, they could
suffer from an XSS attack as some browsers such as Google Chrome ignore control
characters at the start of a URL in an anchor ``href``.

Bugfixes
========

* Fixed renaming of classes in migrations where renaming a subclass would
  cause incorrect state to be recorded for objects that referenced the
  superclass (:ticket:`24354`).

* Stopped writing migration files in dry run mode when merging migration
  conflicts. When ``makemigrations --merge`` is called with ``verbosity=3`` the
  migration file is written to ``stdout`` (:ticket:`24427`).
