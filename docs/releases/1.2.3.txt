==========================
Django 1.2.3 release notes
==========================

Django 1.2.3 fixed a couple of release problems in the 1.2.2 release and was
released two days after 1.2.2.

This release corrects the following problems:

* The :commit:`patch <7f84657b6b2243cc787bdb9f296710c8d13ad0bd>` applied for
  the security issue covered in Django 1.2.2 caused issues with non-ASCII
  responses using CSRF tokens.

* The patch also caused issues with some forms, most notably the user-editing
  forms in the Django administrative interface.

* The packaging manifest did not contain the full list of required files.
