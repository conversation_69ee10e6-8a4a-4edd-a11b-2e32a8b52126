==========================
Django 1.7.1 release notes
==========================

*October 22, 2014*

Django 1.7.1 fixes several bugs in 1.7.

Bugfixes
========

* Allowed related many-to-many fields to be referenced in the admin
  (:ticket:`23604`).

* Added a more helpful error message if you try to migrate an app without first
  creating the ``contenttypes`` table (:ticket:`22411`).

* Modified migrations dependency algorithm to avoid possible infinite recursion.

* Fixed a ``UnicodeDecodeError`` when the ``flush`` error message contained
  Unicode characters (:ticket:`22882`).

* Reinstated missing ``CHECK`` SQL clauses which were omitted on some backends
  when not using migrations (:ticket:`23416`).

* Fixed serialization of ``type`` objects in migrations (:ticket:`22951`).

* Allowed inline and hidden references to admin fields (:ticket:`23431`).

* The ``@deconstructible`` decorator now fails with a ``ValueError`` if the
  decorated object cannot automatically be imported (:ticket:`23418`).

* Fixed a typo in an ``inlineformset_factory()`` error message that caused a
  crash (:ticket:`23451`).

* Restored the ability to use :setting:`ABSOLUTE_URL_OVERRIDES` with the
  ``'auth.User'`` model (:ticket:`11775`). As a side effect, the setting now
  adds a ``get_absolute_url()`` method to any model that appears in
  ``ABSOLUTE_URL_OVERRIDES`` but doesn't define ``get_absolute_url()``.

* Avoided masking some ``ImportError`` exceptions during application loading
  (:ticket:`22920`).

* Empty ``index_together`` or ``unique_together`` model options no longer
  results in infinite migrations (:ticket:`23452`).

* Fixed crash in ``contrib.sitemaps`` if ``lastmod`` returned a ``date`` rather
  than a ``datetime`` (:ticket:`23403`).

* Allowed migrations to work with ``app_label``\s that have the same last
  part (e.g. ``django.contrib.auth`` and ``vendor.auth``) (:ticket:`23483`).

* Restored the ability to deepcopy ``F`` objects (:ticket:`23492`).

* Formats for Welsh (``cy``) and several Chinese locales (``zh_CN``,
  ``zh_Hans``, ``zh_Hant`` and ``zh_TW``) have been added. Formats for
  Macedonian have been fixed (trailing dot removed, :ticket:`23532`).

* Added quoting of constraint names in the SQL generated by migrations to
  prevent crash with uppercase characters in the name (:ticket:`23065`).

* Fixed renaming of models with a self-referential many-to-many field
  (``ManyToManyField('self')``) (:ticket:`23503`).

* Added the :meth:`~django.contrib.admin.InlineModelAdmin.get_extra()`,
  :meth:`~django.contrib.admin.InlineModelAdmin.get_max_num()`, and
  :meth:`~django.contrib.admin.InlineModelAdmin.get_min_num()` hooks to
  :class:`~django.contrib.contenttypes.admin.GenericInlineModelAdmin`
  (:ticket:`23539`).

* Made ``migrations.RunSQL`` no longer require percent sign escaping. This is
  now consistent with ``cursor.execute()`` (:ticket:`23426`).

* Made the ``SERIALIZE`` entry in the :setting:`TEST <DATABASE-TEST>`
  dictionary usable (:ticket:`23421`).

* Fixed bug in migrations that prevented foreign key constraints to unmanaged
  models with a custom primary key (:ticket:`23415`).

* Added ``SchemaEditor`` for MySQL GIS backend so that spatial indexes will be
  created for apps with migrations (:ticket:`23538`).

* Added ``SchemaEditor`` for Oracle GIS backend so that spatial metadata and
  indexes will be created for apps with migrations (:ticket:`23537`).

* Coerced the ``related_name`` model field option to Unicode during migration
  generation to generate migrations that work with both Python 2 and 3
  (:ticket:`23455`).

* Fixed ``MigrationWriter`` to handle builtin types without imports
  (:ticket:`23560`).

* Fixed ``deepcopy`` on ``ErrorList`` (:ticket:`23594`).

* Made the :mod:`~django.contrib.admindocs` view to browse view details check
  if the view specified in the URL exists in the URLconf. Previously it was
  possible to import arbitrary packages from the Python path. This was not
  considered a security issue because ``admindocs`` is only accessible to staff
  users (:ticket:`23601`).

* Fixed ``UnicodeDecodeError`` crash in ``AdminEmailHandler`` with non-ASCII
  characters in the request (:ticket:`23593`).

* Fixed missing ``get_or_create`` and ``update_or_create`` on related managers
  causing ``IntegrityError`` (:ticket:`23611`).

* Made :func:`~django.utils.http.urlsafe_base64_decode` return the proper
  type (bytestring) on Python 3 (:ticket:`23333`).

* :djadmin:`makemigrations` can now serialize timezone-aware values
  (:ticket:`23365`).

* Added a prompt to the migrations questioner when removing the null constraint
  from a field to prevent an IntegrityError on existing NULL rows
  (:ticket:`23609`).

* Fixed generic relations in ``ModelAdmin.list_filter`` (:ticket:`23616`).

* Restored RFC compliance for the SMTP backend on Python 3 (:ticket:`23063`).

* Fixed a crash while parsing cookies containing invalid content
  (:ticket:`23638`).

* The system check framework now raises error **models.E020** when the
  class method ``Model.check()`` is unreachable (:ticket:`23615`).

* Made the Oracle test database creation drop the test user in the event of an
  unclean exit of a previous test run (:ticket:`23649`).

* Fixed :djadmin:`makemigrations` to detect changes to
  :attr:`Meta.db_table <django.db.models.Options.db_table>` (:ticket:`23629`).

* Fixed a regression when feeding the Django test client with an empty data
  string (:ticket:`21740`).

* Fixed a regression in :djadmin:`makemessages` where static files were
  unexpectedly ignored (:ticket:`23583`).
