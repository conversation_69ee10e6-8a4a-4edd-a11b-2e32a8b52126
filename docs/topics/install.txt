=====================
How to install Django
=====================

This document will get you up and running with Django.

Install Python
==============

Django is a Python web framework. See :ref:`faq-python-version-support` for
details.

Get the latest version of Python at https://www.python.org/downloads/ or with
your operating system's package manager.

.. admonition:: Python on Windows

    If you are just starting with Django and using Windows, you may find
    :doc:`/howto/windows` useful.

Install Apache and ``mod_wsgi``
===============================

If you just want to experiment with Django, skip ahead to the next
section; Django includes a lightweight web server you can use for
testing, so you won't need to set up Apache until you're ready to
deploy Django in production.

If you want to use Django on a production site, use `Apache`_ with
`mod_wsgi`_. mod_wsgi operates in one of two modes: embedded
mode or daemon mode. In embedded mode, mod_wsgi is similar to
mod_perl -- it embeds Python within Apache and loads Python code into
memory when the server starts. Code stays in memory throughout the
life of an Apache process, which leads to significant performance
gains over other server arrangements. In daemon mode, mod_wsgi spawns
an independent daemon process that handles requests. The daemon
process can run as a different user than the web server, possibly
leading to improved security. The daemon process can be restarted
without restarting the entire Apache web server, possibly making
refreshing your codebase more seamless. Consult the mod_wsgi
documentation to determine which mode is right for your setup. Make
sure you have Apache installed with the mod_wsgi module activated.
Django will work with any version of Apache that supports mod_wsgi.

See :doc:`How to use Django with mod_wsgi </howto/deployment/wsgi/modwsgi>`
for information on how to configure mod_wsgi once you have it
installed.

If you can't use mod_wsgi for some reason, fear not: Django supports many other
deployment options. One is :doc:`uWSGI </howto/deployment/wsgi/uwsgi>`; it works
very well with `nginx`_. Additionally, Django follows the WSGI spec
(:pep:`3333`), which allows it to run on a variety of server platforms.

.. _Apache: https://httpd.apache.org/
.. _nginx: https://nginx.org/
.. _mod_wsgi: https://modwsgi.readthedocs.io/en/develop/

.. _database-installation:

Get your database running
=========================

If you plan to use Django's database API functionality, you'll need to make
sure a database server is running. Django supports many different database
servers and is officially supported with PostgreSQL_, MariaDB_, MySQL_, Oracle_
and SQLite_.

If you are developing a small project or something you don't plan to deploy in
a production environment, SQLite is generally the best option as it doesn't
require running a separate server. However, SQLite has many differences from
other databases, so if you are working on something substantial, it's
recommended to develop with the same database that you plan on using in
production.

In addition to the officially supported databases, there are :ref:`backends
provided by 3rd parties <third-party-notes>` that allow you to use other
databases with Django.

In addition to a database backend, you'll need to make sure your Python
database bindings are installed.

* If you're using PostgreSQL, you'll need the `psycopg`_ or `psycopg2`_
  package. Refer to the :ref:`PostgreSQL notes <postgresql-notes>` for further
  details.

* If you're using MySQL or MariaDB, you'll need a :ref:`DB API driver
  <mysql-db-api-drivers>` like ``mysqlclient``. See :ref:`notes for the MySQL
  backend <mysql-notes>` for details.

* If you're using SQLite you might want to read the :ref:`SQLite backend notes
  <sqlite-notes>`.

* If you're using Oracle, you'll need a copy of cx_Oracle_, but please
  read the :ref:`notes for the Oracle backend <oracle-notes>` for details
  regarding supported versions of both Oracle and ``cx_Oracle``.

* If you're using an unofficial 3rd party backend, please consult the
  documentation provided for any additional requirements.

If you plan to use Django's ``manage.py migrate`` command to automatically
create database tables for your models (after first installing Django and
creating a project), you'll need to ensure that Django has permission to create
and alter tables in the database you're using; if you plan to manually create
the tables, you can grant Django ``SELECT``, ``INSERT``, ``UPDATE`` and
``DELETE`` permissions. After creating a database user with these permissions,
you'll specify the details in your project's settings file, see
:setting:`DATABASES` for details.

If you're using Django's :doc:`testing framework</topics/testing/index>` to test
database queries, Django will need permission to create a test database.

.. _PostgreSQL: https://www.postgresql.org/
.. _MariaDB: https://mariadb.org/
.. _MySQL: https://www.mysql.com/
.. _psycopg: https://www.psycopg.org/psycopg3/
.. _psycopg2: https://www.psycopg.org/
.. _SQLite: https://www.sqlite.org/
.. _cx_Oracle: https://oracle.github.io/python-cx_Oracle/
.. _Oracle: https://www.oracle.com/

.. _install-django-code:

Install the Django code
=======================

Installation instructions are slightly different depending on whether you're
installing a distribution-specific package, downloading the latest official
release, or fetching the latest development version.

.. _installing-official-release:

Installing an official release with ``pip``
-------------------------------------------

This is the recommended way to install Django.

#. Install pip_. The easiest is to use the `standalone pip installer`_. If your
   distribution already has ``pip`` installed, you might need to update it if
   it's outdated. If it's outdated, you'll know because installation won't
   work.

#. Take a look at :doc:`venv <python:tutorial/venv>`. This tool provides
   isolated Python environments, which are more practical than installing
   packages systemwide. It also allows installing packages without
   administrator privileges. The :doc:`contributing tutorial
   </intro/contributing>` walks through how to create a virtual environment.

#. After you've created and activated a virtual environment, enter the command:

   .. console::

        $ python -m pip install Django

.. _pip: https://pip.pypa.io/
.. _standalone pip installer: https://pip.pypa.io/en/latest/installation/

.. _installing-distribution-package:

Installing a distribution-specific package
------------------------------------------

Check the :doc:`distribution specific notes </misc/distributions>` to see if
your platform/distribution provides official Django packages/installers.
Distribution-provided packages will typically allow for automatic installation
of dependencies and supported upgrade paths; however, these packages will rarely
contain the latest release of Django.

.. _installing-development-version:

Installing the development version
----------------------------------

.. admonition:: Tracking Django development

    If you decide to use the latest development version of Django,
    you'll want to pay close attention to `the development timeline`_,
    and you'll want to keep an eye on the :ref:`release notes for the
    upcoming release <development_release_notes>`. This will help you stay
    on top of any new features you might want to use, as well as any changes
    you'll need to make to your code when updating your copy of Django.
    (For stable releases, any necessary changes are documented in the
    release notes.)

.. _the development timeline: https://code.djangoproject.com/timeline

If you'd like to be able to update your Django code occasionally with the
latest bug fixes and improvements, follow these instructions:

#. Make sure that you have Git_ installed and that you can run its commands
   from a shell. (Enter ``git help`` at a shell prompt to test this.)

#. Check out Django's main development branch like so:

   .. console::

        $ git clone https://github.com/django/django.git

   This will create a directory ``django`` in your current directory.

#. Make sure that the Python interpreter can load Django's code. The most
   convenient way to do this is to use a virtual environment and pip_. The
   :doc:`contributing tutorial </intro/contributing>` walks through how to
   create a virtual environment.

#. After setting up and activating the virtual environment, run the following
   command:

   .. console::

        $ python -m pip install -e django/

   This will make Django's code importable, and will also make the
   ``django-admin`` utility command available. In other words, you're all
   set!

When you want to update your copy of the Django source code, run the command
``git pull`` from within the ``django`` directory. When you do this, Git will
download any changes.

.. _Git: https://git-scm.com/
