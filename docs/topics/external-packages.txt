=================
External packages
=================

Django ships with a variety of extra, optional tools that solve common
problems (``contrib.*``). For easier maintenance and to trim the size of the
codebase, a few of those applications have been moved out to separate projects.

Localflavor
===========

``django-localflavor`` is a collection of utilities for particular countries
and cultures.

* `GitHub <https://github.com/django/django-localflavor>`__
* `Documentation <https://django-localflavor.readthedocs.io/>`__
* :pypi:`PyPI <django-localflavor>`

Comments
========

``django-contrib-comments`` can be used to attach comments to any model, so you
can use it for comments on blog entries, photos, book chapters, or anything
else. Most users will be better served with a custom solution, or a hosted
product like Disqus.

* `GitHub <https://github.com/django/django-contrib-comments>`__
* `Documentation <https://django-contrib-comments.readthedocs.io/>`__
* :pypi:`PyPI <django-contrib-comments>`

Formtools
=========

``django-formtools`` is a collection of assorted utilities to work with forms.

* `GitHub <https://github.com/jazzband/django-formtools/>`__
* `Documentation <https://django-formtools.readthedocs.io/>`__
* :pypi:`PyPI <django-formtools>`
