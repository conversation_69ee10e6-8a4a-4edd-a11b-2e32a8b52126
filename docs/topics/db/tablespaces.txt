===========
Tablespaces
===========

A common paradigm for optimizing performance in database systems is the use of
`tablespaces`_ to organize disk layout.

.. _`tablespaces`: https://en.wikipedia.org/wiki/Tablespace

.. warning::
    Django does not create the tablespaces for you. Please refer to your
    database engine's documentation for details on creating and managing
    tablespaces.


Declaring tablespaces for tables
================================

A tablespace can be specified for the table generated by a model by supplying
the :attr:`~django.db.models.Options.db_tablespace` option inside the model's
``class Meta``. This option also affects tables automatically created for
:class:`~django.db.models.ManyToManyField`\ s in the model.

You can use the :setting:`DEFAULT_TABLESPACE` setting to specify a default value
for :attr:`~django.db.models.Options.db_tablespace`. This is useful for setting
a tablespace for the built-in Django apps and other applications whose code you
cannot control.

Declaring tablespaces for indexes
=================================

You can pass the :attr:`~django.db.models.Index.db_tablespace` option to an
``Index`` constructor to specify the name of a tablespace to use for the index.
For single field indexes, you can pass the
:attr:`~django.db.models.Field.db_tablespace` option to a ``Field`` constructor
to specify an alternate tablespace for the field's column index. If the column
doesn't have an index, the option is ignored.

You can use the :setting:`DEFAULT_INDEX_TABLESPACE` setting to specify
a default value for :attr:`~django.db.models.Field.db_tablespace`.

If :attr:`~django.db.models.Field.db_tablespace` isn't specified and you didn't
set :setting:`DEFAULT_INDEX_TABLESPACE`, the index is created in the same
tablespace as the tables.

An example
==========

.. code-block:: python

    class TablespaceExample(models.Model):
        name = models.CharField(max_length=30, db_index=True, db_tablespace="indexes")
        data = models.CharField(max_length=255, db_index=True)
        shortcut = models.CharField(max_length=7)
        edges = models.ManyToManyField(to="self", db_tablespace="indexes")

        class Meta:
            db_tablespace = "tables"
            indexes = [models.Index(fields=["shortcut"], db_tablespace="other_indexes")]

In this example, the tables generated by the ``TablespaceExample`` model (i.e.
the model table and the many-to-many table) would be stored in the ``tables``
tablespace. The index for the name field and the indexes on the many-to-many
table would be stored in the ``indexes`` tablespace. The ``data`` field would
also generate an index, but no tablespace for it is specified, so it would be
stored in the model tablespace ``tables`` by default. The index for the
``shortcut`` field would be stored in the ``other_indexes`` tablespace.

Database support
================

PostgreSQL and Oracle support tablespaces. SQLite, MariaDB and MySQL don't.

When you use a backend that lacks support for tablespaces, Django ignores all
tablespace-related options.
