====================
Models and databases
====================

.. module:: django.db

A model is the single, definitive source of information about your data. It
contains the essential fields and behaviors of the data you're storing.
Generally, each model maps to a single database table.

.. toctree::
   :maxdepth: 1

   models
   queries
   aggregation
   search
   managers
   sql
   transactions
   multi-db
   tablespaces
   optimization
   instrumentation
   fixtures
   examples/index
