=============================================
``django.urls`` functions for use in URLconfs
=============================================

.. module:: django.urls.conf
    :synopsis: Functions for use in URLconfs.

.. currentmodule:: django.urls

``path()``
==========

.. function:: path(route, view, kwargs=None, name=None)

Returns an element for inclusion in ``urlpatterns``. For example::

    from django.urls import include, path

    urlpatterns = [
        path("index/", views.index, name="main-view"),
        path("bio/<username>/", views.bio, name="bio"),
        path("articles/<slug:title>/", views.article, name="article-detail"),
        path("articles/<slug:title>/<int:section>/", views.section, name="article-section"),
        path("blog/", include("blog.urls")),
        ...,
    ]

The ``route`` argument should be a string or
:func:`~django.utils.translation.gettext_lazy()` (see
:ref:`translating-urlpatterns`) that contains a URL pattern. The string
may contain angle brackets (like ``<username>`` above) to capture part of the
URL and send it as a keyword argument to the view. The angle brackets may
include a converter specification (like the ``int`` part of ``<int:section>``)
which limits the characters matched and may also change the type of the
variable passed to the view. For example, ``<int:section>`` matches a string
of decimal digits and converts the value to an ``int``. See
:ref:`how-django-processes-a-request` for more details.

The ``view`` argument is a view function or the result of
:meth:`~django.views.generic.base.View.as_view` for class-based views. It can
also be an :func:`django.urls.include`.

The ``kwargs`` argument allows you to pass additional arguments to the view
function or method. See :ref:`views-extra-options` for an example.

See :ref:`Naming URL patterns <naming-url-patterns>` for why the ``name``
argument is useful.

``re_path()``
=============

.. function:: re_path(route, view, kwargs=None, name=None)

Returns an element for inclusion in ``urlpatterns``. For example::

    from django.urls import include, re_path

    urlpatterns = [
        re_path(r"^index/$", views.index, name="index"),
        re_path(r"^bio/(?P<username>\w+)/$", views.bio, name="bio"),
        re_path(r"^blog/", include("blog.urls")),
        ...,
    ]

The ``route`` argument should be a string or
:func:`~django.utils.translation.gettext_lazy()` (see
:ref:`translating-urlpatterns`) that contains a regular expression compatible
with Python's :py:mod:`re` module. Strings typically use raw string syntax
(``r''``) so that they can contain sequences like ``\d`` without the need to
escape the backslash with another backslash. When a match is made, captured
groups from the regular expression are passed to the view -- as named arguments
if the groups are named, and as positional arguments otherwise. The values are
passed as strings, without any type conversion.

When a ``route`` ends with ``$`` the whole requested URL, matching against
:attr:`~django.http.HttpRequest.path_info`, must match the regular expression
pattern (:py:func:`re.fullmatch` is used).

The ``view``, ``kwargs`` and ``name`` arguments are the same as for
:func:`~django.urls.path()`.

.. versionchanged:: 2.2.25

    In older versions, a full-match wasn't required for a ``route`` which ends
    with ``$``.

``include()``
=============

.. function:: include(module, namespace=None)
              include(pattern_list)
              include((pattern_list, app_namespace), namespace=None)

    A function that takes a full Python import path to another URLconf module
    that should be "included" in this place. Optionally, the :term:`application
    namespace` and :term:`instance namespace` where the entries will be included
    into can also be specified.

    Usually, the application namespace should be specified by the included
    module. If an application namespace is set, the ``namespace`` argument
    can be used to set a different instance namespace.

    ``include()`` also accepts as an argument either an iterable that returns
    URL patterns or a 2-tuple containing such iterable plus the names of the
    application namespaces.

    :arg module: URLconf module (or module name)
    :arg namespace: Instance namespace for the URL entries being included
    :type namespace: str
    :arg pattern_list: Iterable of :func:`~django.urls.path` and/or :func:`~django.urls.re_path` instances.
    :arg app_namespace: Application namespace for the URL entries being included
    :type app_namespace: str

See :ref:`including-other-urlconfs` and :ref:`namespaces-and-include`.

``register_converter()``
========================

.. function:: register_converter(converter, type_name)

The function for registering a converter for use in :func:`~django.urls.path()`
``route``\s.

The ``converter`` argument is a converter class, and ``type_name`` is the
converter name to use in path patterns. See
:ref:`registering-custom-path-converters` for an example.

==================================================
``django.conf.urls`` functions for use in URLconfs
==================================================

.. module:: django.conf.urls

``static()``
============

.. function:: static.static(prefix, view=django.views.static.serve, **kwargs)

Helper function to return a URL pattern for serving files in debug mode::

    from django.conf import settings
    from django.conf.urls.static import static

    urlpatterns = [
        # ... the rest of your URLconf goes here ...
    ] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

``handler400``
==============

.. data:: handler400

A callable, or a string representing the full Python import path to the view
that should be called if the HTTP client has sent a request that caused an error
condition and a response with a status code of 400.

By default, this is :func:`django.views.defaults.bad_request`. If you
implement a custom view, be sure it accepts ``request`` and ``exception``
arguments and returns an :class:`~django.http.HttpResponseBadRequest`.

``handler403``
==============

.. data:: handler403

A callable, or a string representing the full Python import path to the view
that should be called if the user doesn't have the permissions required to
access a resource.

By default, this is :func:`django.views.defaults.permission_denied`. If you
implement a custom view, be sure it accepts ``request`` and ``exception``
arguments and returns an :class:`~django.http.HttpResponseForbidden`.

``handler404``
==============

.. data:: handler404

A callable, or a string representing the full Python import path to the view
that should be called if none of the URL patterns match.

By default, this is :func:`django.views.defaults.page_not_found`. If you
implement a custom view, be sure it accepts ``request`` and ``exception``
arguments and returns an :class:`~django.http.HttpResponseNotFound`.

``handler500``
==============

.. data:: handler500

A callable, or a string representing the full Python import path to the view
that should be called in case of server errors. Server errors happen when you
have runtime errors in view code.

By default, this is :func:`django.views.defaults.server_error`. If you
implement a custom view, be sure it accepts a ``request`` argument and returns
an :class:`~django.http.HttpResponseServerError`.
