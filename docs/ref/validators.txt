==========
Validators
==========

.. module:: django.core.validators
    :synopsis: Validation utilities and base classes

Writing validators
==================

A validator is a callable that takes a value and raises a
:exc:`~django.core.exceptions.ValidationError` if it doesn't meet some
criteria. Validators can be useful for reusing validation logic between
different types of fields.

For example, here's a validator that only allows even numbers::

    from django.core.exceptions import ValidationError
    from django.utils.translation import gettext_lazy as _


    def validate_even(value):
        if value % 2 != 0:
            raise ValidationError(
                _("%(value)s is not an even number"),
                params={"value": value},
            )

You can add this to a model field via the field's :attr:`~django.db.models.Field.validators`
argument::

    from django.db import models


    class MyModel(models.Model):
        even_field = models.IntegerField(validators=[validate_even])

Because values are converted to Python before validators are run, you can even
use the same validator with forms::

    from django import forms


    class MyForm(forms.Form):
        even_field = forms.IntegerField(validators=[validate_even])

You can also use a class with a ``__call__()`` method for more complex or
configurable validators. :class:`RegexValidator`, for example, uses this
technique. If a class-based validator is used in the
:attr:`~django.db.models.Field.validators` model field option, you should make
sure it is :ref:`serializable by the migration framework
<migration-serializing>` by adding :ref:`deconstruct()
<custom-deconstruct-method>` and ``__eq__()`` methods.

How validators are run
======================

See the :doc:`form validation </ref/forms/validation>` for more information on
how validators are run in forms, and :ref:`Validating objects
<validating-objects>` for how they're run in models. Note that validators will
not be run automatically when you save a model, but if you are using a
:class:`~django.forms.ModelForm`, it will run your validators on any fields
that are included in your form. See the
:doc:`ModelForm documentation </topics/forms/modelforms>` for information on
how model validation interacts with forms.

Built-in validators
===================

The :mod:`django.core.validators` module contains a collection of callable
validators for use with model and form fields. They're used internally but
are available for use with your own fields, too. They can be used in addition
to, or in lieu of custom ``field.clean()`` methods.

``RegexValidator``
------------------

.. class:: RegexValidator(regex=None, message=None, code=None, inverse_match=None, flags=0)

    :param regex: If not ``None``, overrides :attr:`regex`. Can be a regular
        expression string or a pre-compiled regular expression.
    :param message: If not ``None``, overrides :attr:`.message`.
    :param code: If not ``None``, overrides :attr:`code`.
    :param inverse_match: If not ``None``, overrides :attr:`inverse_match`.
    :param flags: If not ``None``, overrides :attr:`flags`. In that case,
        :attr:`regex` must be a regular expression string, or
        :exc:`TypeError` is raised.

    A :class:`RegexValidator` searches the provided ``value`` for a given
    regular expression with :func:`re.search`. By default, raises a
    :exc:`~django.core.exceptions.ValidationError` with :attr:`message` and
    :attr:`code` if a match **is not** found. Its behavior can be inverted by
    setting :attr:`inverse_match` to ``True``, in which case the
    :exc:`~django.core.exceptions.ValidationError` is raised when a match
    **is** found.

    .. attribute:: regex

        The regular expression pattern to search for within the provided
        ``value``, using :func:`re.search`. This may be a string or a
        pre-compiled regular expression created with :func:`re.compile`.
        Defaults to the empty string, which will be found in every possible
        ``value``.

    .. attribute:: message

        The error message used by
        :exc:`~django.core.exceptions.ValidationError` if validation fails.
        Defaults to ``"Enter a valid value"``.

    .. attribute:: code

        The error code used by :exc:`~django.core.exceptions.ValidationError`
        if validation fails. Defaults to ``"invalid"``.

    .. attribute:: inverse_match

        The match mode for :attr:`regex`. Defaults to ``False``.

    .. attribute:: flags

        The :ref:`regex flags <python:contents-of-module-re>` used when
        compiling the regular expression string :attr:`regex`. If :attr:`regex`
        is a pre-compiled regular expression, and :attr:`flags` is overridden,
        :exc:`TypeError` is raised. Defaults to ``0``.

``EmailValidator``
------------------

.. class:: EmailValidator(message=None, code=None, allowlist=None)

    :param message: If not ``None``, overrides :attr:`.message`.
    :param code: If not ``None``, overrides :attr:`code`.
    :param allowlist: If not ``None``, overrides :attr:`allowlist`.

    .. attribute:: message

        The error message used by
        :exc:`~django.core.exceptions.ValidationError` if validation fails.
        Defaults to ``"Enter a valid email address"``.

    .. attribute:: code

        The error code used by :exc:`~django.core.exceptions.ValidationError`
        if validation fails. Defaults to ``"invalid"``.

    .. attribute:: allowlist

        Allowlist of email domains. By default, a regular expression (the
        ``domain_regex`` attribute) is used to validate whatever appears after
        the ``@`` sign. However, if that string appears in the ``allowlist``,
        this validation is bypassed. If not provided, the default ``allowlist``
        is ``['localhost']``. Other domains that don't contain a dot won't pass
        validation, so you'd need to add them to the ``allowlist`` as
        necessary.

``URLValidator``
----------------

.. class:: URLValidator(schemes=None, regex=None, message=None, code=None)

    A :class:`RegexValidator` subclass that ensures a value looks like a URL,
    and raises an error code of ``'invalid'`` if it doesn't.

    Loopback addresses and reserved IP spaces are considered valid. Literal
    IPv6 addresses (:rfc:`3986#section-3.2.2`) and Unicode domains are both
    supported.

    In addition to the optional arguments of its parent :class:`RegexValidator`
    class, ``URLValidator`` accepts an extra optional attribute:

    .. attribute:: schemes

        URL/URI scheme list to validate against. If not provided, the default
        list is ``['http', 'https', 'ftp', 'ftps']``. As a reference, the IANA
        website provides a full list of `valid URI schemes`_.

        .. _valid URI schemes: https://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml

``validate_email``
------------------

.. data:: validate_email

    An :class:`EmailValidator` instance without any customizations.

``validate_slug``
-----------------

.. data:: validate_slug

    A :class:`RegexValidator` instance that ensures a value consists of only
    letters, numbers, underscores or hyphens.

``validate_unicode_slug``
-------------------------

.. data:: validate_unicode_slug

    A :class:`RegexValidator` instance that ensures a value consists of only
    Unicode letters, numbers, underscores, or hyphens.

``validate_ipv4_address``
-------------------------

.. data:: validate_ipv4_address

    A :class:`RegexValidator` instance that ensures a value looks like an IPv4
    address.

``validate_ipv6_address``
-------------------------

.. data:: validate_ipv6_address

    Uses ``django.utils.ipv6`` to check the validity of an IPv6 address.

``validate_ipv46_address``
--------------------------

.. data:: validate_ipv46_address

    Uses both ``validate_ipv4_address`` and ``validate_ipv6_address`` to
    ensure a value is either a valid IPv4 or IPv6 address.

``validate_comma_separated_integer_list``
-----------------------------------------

.. data:: validate_comma_separated_integer_list

    A :class:`RegexValidator` instance that ensures a value is a
    comma-separated list of integers.

``int_list_validator``
----------------------

.. function:: int_list_validator(sep=',', message=None, code='invalid', allow_negative=False)

    Returns a :class:`RegexValidator` instance that ensures a string consists
    of integers separated by ``sep``. It allows negative integers when
    ``allow_negative`` is ``True``.

``MaxValueValidator``
---------------------

.. class:: MaxValueValidator(limit_value, message=None)

    Raises a :exc:`~django.core.exceptions.ValidationError` with a code of
    ``'max_value'`` if ``value`` is greater than ``limit_value``, which may be
    a callable.

``MinValueValidator``
---------------------

.. class:: MinValueValidator(limit_value, message=None)

    Raises a :exc:`~django.core.exceptions.ValidationError` with a code of
    ``'min_value'`` if ``value`` is less than ``limit_value``, which may be a
    callable.

``MaxLengthValidator``
----------------------

.. class:: MaxLengthValidator(limit_value, message=None)

    Raises a :exc:`~django.core.exceptions.ValidationError` with a code of
    ``'max_length'`` if the length of ``value`` is greater than
    ``limit_value``, which may be a callable.

``MinLengthValidator``
----------------------

.. class:: MinLengthValidator(limit_value, message=None)

    Raises a :exc:`~django.core.exceptions.ValidationError` with a code of
    ``'min_length'`` if the length of ``value`` is less than ``limit_value``,
    which may be a callable.

``DecimalValidator``
--------------------

.. class:: DecimalValidator(max_digits, decimal_places)

    Raises :exc:`~django.core.exceptions.ValidationError` with the following
    codes:

    - ``'max_digits'`` if the number of digits is larger than ``max_digits``.
    - ``'max_decimal_places'`` if the number of decimals is larger than
      ``decimal_places``.
    - ``'max_whole_digits'`` if the number of whole digits is larger than
      the difference between ``max_digits`` and ``decimal_places``.

``FileExtensionValidator``
--------------------------

.. class:: FileExtensionValidator(allowed_extensions, message, code)

    Raises a :exc:`~django.core.exceptions.ValidationError` with a code of
    ``'invalid_extension'`` if the extension of ``value.name`` (``value`` is
    a :class:`~django.core.files.File`) isn't found in ``allowed_extensions``.
    The extension is compared case-insensitively with ``allowed_extensions``.

    .. warning::

        Don't rely on validation of the file extension to determine a file's
        type. Files can be renamed to have any extension no matter what data
        they contain.

``validate_image_file_extension``
---------------------------------

.. data:: validate_image_file_extension

    Uses Pillow to ensure that ``value.name`` (``value`` is a
    :class:`~django.core.files.File`) has `a valid image extension
    <https://pillow.readthedocs.io/en/latest/handbook/image-file-formats.html>`_.

``ProhibitNullCharactersValidator``
-----------------------------------

.. class:: ProhibitNullCharactersValidator(message=None, code=None)

    Raises a :exc:`~django.core.exceptions.ValidationError` if ``str(value)``
    contains one or more null characters (``'\x00'``).

    :param message: If not ``None``, overrides :attr:`.message`.
    :param code: If not ``None``, overrides :attr:`code`.

    .. attribute:: message

        The error message used by
        :exc:`~django.core.exceptions.ValidationError` if validation fails.
        Defaults to ``"Null characters are not allowed."``.

    .. attribute:: code

        The error code used by :exc:`~django.core.exceptions.ValidationError`
        if validation fails. Defaults to ``"null_characters_not_allowed"``.

``StepValueValidator``
----------------------

.. class:: StepValueValidator(limit_value, message=None)

    Raises a :exc:`~django.core.exceptions.ValidationError` with a code of
    ``'step_size'`` if ``value`` is not an integral multiple of
    ``limit_value``, which can be a float, integer or decimal value or a
    callable.
