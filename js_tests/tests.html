<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Django JavaScript Tests</title>
    <link rel="stylesheet" href="../node_modules/qunit/qunit/qunit.css">
</head>
<body>

    <div id="qunit"></div>
    <div id="qunit-fixture">
    </div>
    <script type="text/html" id="result-table">
        <form id="changelist-form">
            <button type="submit" class="button" name="index" value="0">Go</button>
            <span class="action-counter" data-actions-icnt="100"></span>
            <table id="result_list">
                <tr>
                    <th>
                       <input type="checkbox" id="action-toggle" aria-label="Select all objects on this page for an action">
                    </th>
                </tr>
                <tr>
                    <td class="action-checkbox">
                        <input class="action-select" type="checkbox" value="618">
                    </td>
                </tr>
            </table>
            <input type="submit" name="_save" value="Save">
        </form>
    </script>
    <script type="text/html" id="tabular-formset">
        <input id="id_first-TOTAL_FORMS" value="1">
        <input id="id_first-MAX_NUM_FORMS" value="">
        <table class="inline">
            <tr id="first-0" class="form-row">
                <td class="field-test_field">
                    <input id="id_first-0-test_field">
                </td>
            </tr>
            <tr id="first-empty" class="form-row empty-form">
                <td class="field-test_field">
                    <input id="id_first-__prefix__-test_field">
                </td>
            </tr>
        </table>
    </script>
    <script type="text/html" id="tabular-formset-with-validation-error">
        <div class="inline-group">
            <div class="tabular inline-related">
                <input id="id_second-TOTAL_FORMS" value="2">
                <input id="id_second-MAX_NUM_FORMS" value="">
                <input id="id_second-MIN_NUM_FORMS" value="">
                <table class="inline">
                    <tr id="second-0" class="form-row has_original">
                        <td class="field-test_field">
                            <input id="id_second-0-test_field">
                        </td>
                        <td class="delete">
                          <input type="checkbox" />
                        </td>
                    </tr>
                    <tr class="row-form-errors">
                      <td colspan="2">
                        <ul class="errorlist nonfield">
                          <li>This next form has non-field errors.</li>
                        </ul>
                      </td>
                    </tr>
                    <tr id="second-1" class="form-row">
                        <td class="field-test_field">
                            <input id="id_second-1-test_field">
                        </td>
                        <td class="delete"></td>
                    </tr>
                    <tr id="second-empty" class="form-row empty-form">
                        <td class="field-test_field">
                            <input id="id_second-__prefix__-test_field">
                        </td>
                        <td class="delete"></td>
                    </tr>
                </table>
            </div>
        </div>
    </script>
    <script type="text/html" id="nav-sidebar-filter">
      <nav class="sticky" id="nav-sidebar">
        <input type="search" id="nav-filter"
               placeholder="Start typing to filter..."
               aria-label="Filter navigation items">
        <div class="app-auth module current-app">
          <table>
            <caption>
              <a href="/admin/auth/" class="section"
                 title="Models in the Authentication and Authorization application">
                    Authentication and Authorization
              </a>
            </caption>
            <tbody>
            <tr class="model-group">
              <th scope="row"><a href="/admin/auth/group/">Groups</a></th>
              <td><a href="/admin/auth/group/add/" class="addlink">Add</a></td>
            </tr>
            <tr class="model-user current-model">
              <th scope="row"><a href="/admin/auth/user/" aria-current="page">Users</a></th>
              <td><a href="/admin/auth/user/add/" class="addlink">Add</a></td>
            </tr>
            </tbody>
          </table>
        </div>
      </nav>
    </script>

    <script src="../node_modules/qunit/qunit/qunit.js"></script>

    <script src='../django/contrib/admin/static/admin/js/vendor/xregexp/xregexp.min.js'></script>
    <script src='../django/contrib/admin/static/admin/js/vendor/jquery/jquery.min.js'></script>
    <script src='../django/contrib/admin/static/admin/js/jquery.init.js'></script>
    <script src='./admin/jsi18n-mocks.test.js'></script>

    <script src='../django/contrib/admin/static/admin/js/core.js' data-cover></script>
    <script src='./admin/core.test.js'></script>

    <script src='../django/contrib/admin/static/admin/js/nav_sidebar.js' data-cover></script>
    <script src='./admin/navigation.test.js'></script>

    <script src='../django/contrib/admin/static/admin/js/admin/RelatedObjectLookups.js' data-cover></script>

    <script src='./admin/DateTimeShortcuts.test.js'></script>
    <script src='../django/contrib/admin/static/admin/js/calendar.js' data-cover></script>
    <script src='../django/contrib/admin/static/admin/js/admin/DateTimeShortcuts.js' data-cover></script>

    <script src='../django/contrib/admin/static/admin/js/actions.js' data-cover></script>
    <script src='./admin/actions.test.js'></script>

    <script src='../django/contrib/admin/static/admin/js/SelectBox.js' data-cover></script>
    <script src='./admin/SelectBox.test.js'></script>

    <script src='../django/contrib/admin/static/admin/js/SelectFilter2.js' data-cover></script>
    <script src='./admin/SelectFilter2.test.js'></script>

    <script src='../django/contrib/admin/static/admin/js/inlines.js' data-cover></script>
    <script src='./admin/inlines.test.js'></script>

    <script src='../django/contrib/admin/static/admin/js/actions.js' data-cover></script>
    <script src='../django/contrib/admin/static/admin/js/collapse.js' data-cover></script>
    <script src='../django/contrib/admin/static/admin/js/prepopulate.js' data-cover></script>
    <script src='../django/contrib/admin/static/admin/js/urlify.js' data-cover></script>
    <script src='./admin/URLify.test.js'></script>

    <div id="id_point_map" style="display:none;">
        <textarea id="id_point" name="point" class="vSerializedField required"
                  style="display:none;" rows="10" cols="150"
        >{&quot;type&quot;: &quot;Point&quot;, &quot;coordinates&quot;: [7.8177, 47.397]}</textarea>
    </div>
    <div id="id_multipolygon_map" style="display:none;">
        <textarea id="id_multipolygon" name="multipolygon" class="vSerializedField required"
                  style="display:none;" rows="10" cols="150"></textarea>
    </div>
    <script src='https://cdn.jsdelivr.net/npm/ol@v7.2.2/dist/ol.js'></script>
    <script src='../django/contrib/gis/static/gis/js/OLMapWidget.js' data-cover></script>
    <script src='./gis/mapwidget.test.js'></script>

</body>
</html>
